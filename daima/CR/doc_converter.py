import pythoncom
from win32com.client import Dispatch
import os
import time

def convert_to_docx(doc_path, max_retries=3):
    """
    将 .doc 文件转换为 .docx 文件

    :param doc_path: .doc 文件的路径
    :param max_retries: 最大重试次数
    :return: 转换后的 .docx 文件路径，如果转换失败则返回 None
    """
    retries = 0
    while retries < max_retries:
        word = None
        try:
            pythoncom.CoInitialize()
            word = Dispatch("Word.Application")
            word.Visible = False
            word.DisplayAlerts = 0
            docx_path = doc_path.replace(".doc", ".docx")

            try:
                doc = word.Documents.Open(doc_path)
                doc.SaveAs(docx_path, FileFormat=16)
                doc.Close()
                os.remove(doc_path)
                print(f"成功转换并删除原文件: {doc_path}")
                return docx_path
            except Exception as e:
                print(f"第 {retries + 1} 次尝试转换文件失败：{doc_path}，错误信息：{e}")
                retries += 1
                if retries < max_retries:
                    time.sleep(2)  # 等待 2 秒后重试
        except ImportError:
            print("pywin32 库未安装，请使用 'pip install pywin32' 进行安装。")
            return None
        finally:
            if word is not None:
                try:
                    word.Quit()
                except Exception as e:
                    print(f"关闭 Word 应用程序失败，错误信息：{e}")
    print(f"达到最大重试次数，放弃转换：{doc_path}")
    return None