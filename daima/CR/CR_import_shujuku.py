import os
import sqlite3
from docx import Document
from docx.image.exceptions import UnrecognizedImageError
from doc_converter import convert_to_docx
# 导入 show_images_gui 函数
from show_images_from_db import show_images_gui

# 解析 Word 表格数据
def extract_data_from_docx(file_path):
    try:
        doc = Document(file_path)
        extracted_data = {
            "CR_number": None, "CR_version": None, "FRR_number": None, "aircraft_type": None,
            "msn": None, "part_number": None, "drawing_number": None, "part_description": None,
            "part_revision": None, "part_serial_no": None, "quantity": None, "attached_pages": None, "Suffix": None,
            "divergence_descriptions": "",
            "customer_impact_description": "",
            "impacted_manuals": ""
        }

        # 只遍历前两页的表格
        for i, table in enumerate(doc.tables):
            if i >= 2:
                break
            for row_index, row in enumerate(table.rows):
                for col_index, cell in enumerate(row.cells):
                    text = cell.text.strip()
                    if "2 编号" in text and col_index + 1 < len(row.cells):
                        extracted_data["CR_number"] = row.cells[col_index + 1].text.strip()
                    if "FRR No." in text and col_index + 1 < len(row.cells):
                        extracted_data["FRR_number"] = row.cells[col_index + 1].text.strip()
                    if "5 版次" in text and col_index + 1 < len(row.cells):
                        extracted_data["CR_version"] = row.cells[col_index + 1].text.strip()
                    if "机型" in text and col_index + 1 < len(row.cells):
                        extracted_data["aircraft_type"] = row.cells[col_index + 1].text.strip()
                    if "4 制造序列号" in text and col_index + 1 < len(row.cells):
                        extracted_data["msn"] = row.cells[col_index + 1].text.strip()
                    if "6零件号" in text and col_index + 1 < len(row.cells):
                        extracted_data["part_number"] = row.cells[col_index + 1].text.strip()
                    if "7 图号" in text and col_index + 1 < len(row.cells):
                        extracted_data["drawing_number"] = row.cells[col_index + 1].text.strip()
                    if "9 零件名称" in text and col_index + 1 < len(row.cells):
                        extracted_data["part_description"] = row.cells[col_index + 1].text.strip()
                    if ("10 零件版次" in text or "10 数模版次" in text) and col_index + 1 < len(row.cells):
                        extracted_data["part_revision"] = row.cells[col_index + 1].text.strip()
                    if "11 系列号" in text and col_index + 1 < len(row.cells):
                        extracted_data["part_serial_no"] = row.cells[col_index + 1].text.strip()
                    if "12 数量" in text and col_index + 1 < len(row.cells):
                        extracted_data["quantity"] = row.cells[col_index + 1].text.strip()
                    if "13 附件：" in text and col_index + 1 < len(row.cells):
                        extracted_data["attached_pages"] = row.cells[col_index + 1].text.strip()
                    if i == 0 and "影响分类" in text and col_index + 1 < len(row.cells):
                        extracted_data["Suffix"] = row.cells[col_index + 1].text.strip()

                    if "14 偏差描述：" in text or "Description of divergence" in text:
                        next_row_index = row_index + 1
                        if next_row_index < len(table.rows):
                            next_cell = table.rows[next_row_index].cells[col_index]
                            next_cell_text = next_cell.text.strip()
                            if next_cell_text not in extracted_data["divergence_descriptions"]:
                                if extracted_data["divergence_descriptions"]:
                                    extracted_data["divergence_descriptions"] += " " + next_cell_text
                                else:
                                    extracted_data["divergence_descriptions"] = next_cell_text
                    # 仅在第一页表格中提取客户影响描述
                    if i == 0 and ("15 客户影响描述" in text or "Description of impact on customer" in text):
                        next_row_index = row_index + 1
                        if next_row_index < len(table.rows):
                            next_cell = table.rows[next_row_index].cells[col_index]
                            extracted_data["customer_impact_description"] = next_cell.text.strip()
                    # 仅在第一页表格中提取受影响的手册或服务类文件内容
                    if i == 0 and ("17 受影响的手册或服务类文件" in text or "Impacted Manual or Service documents" in text):
                        next_row_index = row_index + 1
                        if next_row_index < len(table.rows):
                            next_cell = table.rows[next_row_index].cells[col_index]
                            extracted_data["impacted_manuals"] = next_cell.text.strip()

        # 提取图片
        images = []
        for rel in doc.part.rels.values():
            if "image" in rel.reltype:
                try:
                    image = rel.target_part.blob
                    images.append(image)
                except UnrecognizedImageError:
                    print(f"无法识别 {file_path} 中的图片。")

        return extracted_data, images
    except Exception as e:
        print(f"解析 Word 文件失败：{file_path}，错误信息：{e}")
        return None, []

# 连接数据库并创建表格
def initialize_database(db_path):
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS WordData (
            ID INTEGER PRIMARY KEY AUTOINCREMENT,
            CR_number TEXT,
            CR_version TEXT,
            FRR_number TEXT,
            aircraft_type TEXT,
            msn TEXT,
            part_number TEXT,
            drawing_number TEXT,
            part_description TEXT,
            part_revision TEXT,
            part_serial_no TEXT,
            quantity TEXT,
            attached_pages TEXT,
            Suffix TEXT,
            divergence_descriptions TEXT,
            customer_impact_description TEXT,
            impacted_manuals TEXT,
            document_path TEXT,
            UNIQUE(CR_number, CR_version)
        )
    """)
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS Images (
            ID INTEGER PRIMARY KEY AUTOINCREMENT,
            document_path TEXT,
            image_data BLOB,
            FOREIGN KEY (document_path) REFERENCES WordData(document_path)
        )
    """)
    conn.commit()
    return conn, cursor

# 主函数：遍历文件夹并处理 Word 文件
def process_word_files(folder_path, db_path):
    conn, cursor = initialize_database(db_path)
    data_to_insert = []
    image_data_to_insert = []

    for filename in os.listdir(folder_path):
        file_path = os.path.join(folder_path, filename)

        # 处理 .doc 文件转换
        if filename.endswith(".doc"):
            print(f"正在转换 {file_path} 为 .docx...")
            # 调用新模块中的函数
            new_file_path = convert_to_docx(file_path)
            if not new_file_path:
                print(f"文件转换失败，跳过：{filename}")
                continue
            file_path = new_file_path
        elif not filename.endswith(".docx"):
            print(f"跳过不支持的文件格式：{filename}")
            continue

        print(f"正在处理文件：{file_path}")
        extracted_data, images = extract_data_from_docx(file_path)

        if extracted_data and any(extracted_data.values()):
            extracted_data["document_path"] = file_path
            data_to_insert.append(tuple(extracted_data.values()))
            for image in images:
                image_data_to_insert.append((file_path, image))

    # 批量插入数据库
    if data_to_insert:
        try:
            cursor.executemany("""
                INSERT OR IGNORE INTO WordData (CR_number, CR_version, FRR_number, aircraft_type, msn, part_number, 
                                                drawing_number, part_description, part_revision, part_serial_no, 
                                                quantity, attached_pages, Suffix, divergence_descriptions, 
                                                customer_impact_description,
                                                impacted_manuals,
                                                document_path
                                                ) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, data_to_insert)
            conn.commit()
            print(f"成功插入 {len(data_to_insert)} 条数据到 WordData 表")
        except Exception as e:
            print(f"数据库插入到 WordData 表失败：{e}")

    if image_data_to_insert:
        try:
            cursor.executemany("""
                INSERT INTO Images (document_path, image_data)
                VALUES (?, ?)
            """, image_data_to_insert)
            conn.commit()
            print(f"成功插入 {len(image_data_to_insert)} 条图片数据到 Images 表")
        except Exception as e:
            print(f"数据库插入到 Images 表失败：{e}")

    conn.close()
    print("数据提取和存储完成！")

# 设置文件夹路径和数据库路径
folder_path = r"D:\shangfei\sf\11\daima\CR\10177\客服已完成"
db_path = r"D:\shangfei\sf\11\daima\CR\shujuku.db"

# 运行主程序
process_word_files(folder_path, db_path)

# 调用 show_images_gui 函数显示图片查看器
show_images_gui(db_path)