#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动化文件下载系统 - 主程序
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def check_dependencies():
    """检查依赖包"""
    missing_packages = []
    
    try:
        import selenium
    except ImportError:
        missing_packages.append("selenium")
    
    try:
        import requests
    except ImportError:
        missing_packages.append("requests")
    
    if missing_packages:
        error_msg = f"缺少以下依赖包：{', '.join(missing_packages)}\n\n"
        error_msg += "请运行以下命令安装：\n"
        error_msg += f"pip install {' '.join(missing_packages)}"
        
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("依赖包缺失", error_msg)
        return False
    
    return True

def check_chrome_driver():
    """检查Chrome驱动"""
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        
        options = Options()
        options.add_argument("--headless")
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        
        driver = webdriver.Chrome(options=options)
        driver.quit()
        return True
        
    except Exception as e:
        error_msg = "Chrome驱动未正确安装或配置！\n\n"
        error_msg += "请确保：\n"
        error_msg += "1. 已安装Chrome浏览器\n"
        error_msg += "2. 已安装ChromeDriver\n"
        error_msg += "3. ChromeDriver在系统PATH中\n\n"
        error_msg += f"错误详情：{str(e)}"
        
        root = tk.Tk()
        root.withdraw()
        messagebox.showwarning("Chrome驱动问题", error_msg)
        return False

def main():
    """主函数"""
    print("🚀 启动自动化文件下载系统...")
    
    # 检查依赖
    if not check_dependencies():
        return
    
    # 检查Chrome驱动（非阻塞性检查）
    check_chrome_driver()
    
    try:
        # 导入并启动GUI
        from download_gui import main as gui_main
        gui_main()
        
    except Exception as e:
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("启动错误", f"程序启动失败：{str(e)}")

if __name__ == "__main__":
    main()
