#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试图片查看器界面
"""

import os
from show_images_from_db import show_images_gui

def main():
    print("=" * 50)
    print("图片查看器测试")
    print("=" * 50)
    
    # 数据库路径
    db_path = r"D:\shangfei\sf\11\daima\CR\shujuku.db"
    
    # 检查数据库是否存在
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        print("请先运行 CR_import_shujuku.py 来创建数据库")
        input("按回车键退出...")
        return
    
    print(f"✅ 找到数据库: {db_path}")
    print("\n界面改进:")
    print("✅ 文档选择框宽度增加，可以显示完整的Word文件名")
    print("✅ 上一张/下一张按钮移到了查看图片按钮旁边")
    print("✅ 图片索引信息居中显示")
    print("✅ 窗口尺寸增加到1400x950px")
    print("✅ 新增文档路径显示区域")
    print("✅ 支持一键复制文档路径到剪贴板")
    print("\n正在启动图片查看器...")
    
    try:
        # 启动图片查看器
        show_images_gui(db_path)
    except Exception as e:
        print(f"❌ 启动图片查看器失败: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
