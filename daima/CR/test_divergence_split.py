#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试偏差描述分页逻辑
"""

def parse_divergence_descriptions(divergence_text):
    """
    解析偏差描述，正确分离内容：
    第一页：区域 + 位置 + 初始状态
    第二页：最终状态
    """
    if not divergence_text:
        return "", ""
    
    # 查找关键词位置
    final_keywords = ["最终状态：", "Final state"]
    
    # 查找最终状态的开始位置
    final_start_pos = -1
    for keyword in final_keywords:
        pos = divergence_text.find(keyword)
        if pos != -1:
            final_start_pos = pos
            break
    
    if final_start_pos != -1:
        # 找到最终状态，进行分割
        # 第一页内容：从开始到最终状态之前（包含区域、位置、初始状态）
        first_page_content = divergence_text[:final_start_pos].strip()
        
        # 第二页内容：最终状态及其后的内容
        second_page_content = divergence_text[final_start_pos:].strip()
        
        return first_page_content, second_page_content
    else:
        # 没有找到最终状态关键词，所有内容放在第一页
        return divergence_text.strip(), ""

def test_parsing():
    """测试解析功能"""
    
    # 测试用例：你提供的格式
    test_case = """区域： 
area
位置：
Localization
初始状态：
Initial state
这里是初始状态的详细描述内容。
最终状态：
Final state
这里是最终状态的详细描述内容。"""
    
    print("🧪 偏差描述分页测试")
    print("=" * 60)
    
    print("📝 原始偏差描述:")
    print("-" * 40)
    print(test_case)
    
    print("\n🔄 解析结果:")
    print("-" * 40)
    
    first_page, second_page = parse_divergence_descriptions(test_case)
    
    print("📄 第一页内容（区域 + 位置 + 初始状态）:")
    print("┌" + "─" * 50 + "┐")
    for line in first_page.split('\n'):
        print(f"│ {line:<48} │")
    print("└" + "─" * 50 + "┘")
    
    print("\n📄 第二页内容（最终状态）:")
    print("┌" + "─" * 50 + "┐")
    for line in second_page.split('\n'):
        print(f"│ {line:<48} │")
    print("└" + "─" * 50 + "┘")
    
    # 验证结果
    print("\n✅ 验证结果:")
    print(f"   第一页包含区域: {'区域：' in first_page}")
    print(f"   第一页包含位置: {'位置：' in first_page}")
    print(f"   第一页包含初始状态: {'初始状态：' in first_page}")
    print(f"   第二页包含最终状态: {'最终状态：' in second_page}")
    print(f"   第二页不包含区域: {'区域：' not in second_page}")

def test_with_database():
    """从数据库中测试实际数据"""
    import sqlite3
    import os
    
    db_path = r"D:\shangfei\sf\11\daima\CR\shujuku.db"
    
    if not os.path.exists(db_path):
        print(f"\n❌ 数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 查询包含偏差描述的记录
        cursor.execute("""
            SELECT CR_number, divergence_descriptions 
            FROM WordData 
            WHERE divergence_descriptions IS NOT NULL 
            AND divergence_descriptions != ''
            AND divergence_descriptions LIKE '%最终状态%'
            LIMIT 3
        """)
        
        rows = cursor.fetchall()
        conn.close()
        
        if not rows:
            print("\n❌ 数据库中没有找到包含最终状态的偏差描述数据")
            return
        
        print("\n" + "=" * 60)
        print("📊 数据库实际数据测试")
        print("=" * 60)
        
        for i, (cr_number, divergence_text) in enumerate(rows, 1):
            print(f"\n📋 测试 {i}: CR编号 {cr_number}")
            print("-" * 40)
            
            first_page, second_page = parse_divergence_descriptions(divergence_text)
            
            print("第一页长度:", len(first_page), "字符")
            print("第二页长度:", len(second_page), "字符")
            
            print("第一页预览:", first_page[:100] + "..." if len(first_page) > 100 else first_page)
            print("第二页预览:", second_page[:100] + "..." if len(second_page) > 100 else second_page)
            
    except Exception as e:
        print(f"❌ 数据库查询失败: {e}")

if __name__ == "__main__":
    test_parsing()
    test_with_database()
    
    print("\n" + "=" * 60)
    print("🎯 总结:")
    print("✅ 第一页包含：区域 + 位置 + 初始状态")
    print("✅ 第二页包含：最终状态")
    print("✅ 分页逻辑正确")
    
    input("\n按回车键退出...")
