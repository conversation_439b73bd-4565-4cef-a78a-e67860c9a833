#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动化文件下载器
支持根据架次号自动登录网站并下载相关文件
"""

import os
import time
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import requests
from urllib.parse import urljoin, urlparse
import threading
from datetime import datetime

class AutoDownloader:
    def __init__(self, config_file="config.json"):
        """初始化下载器"""
        self.config_file = config_file
        self.config = self.load_config()
        self.driver = None
        self.is_logged_in = False
        self.download_progress = {}
        
    def load_config(self):
        """加载配置文件"""
        default_config = {
            "website_url": "https://example.com",
            "username": "",
            "password": "",
            "download_folder": "./downloads",
            "chrome_driver_path": "",
            "login_selectors": {
                "username_field": "input[name='username']",
                "password_field": "input[name='password']",
                "login_button": "button[type='submit']"
            },
            "search_selectors": {
                "search_box": "input[name='search']",
                "search_button": "button[type='submit']",
                "result_links": "a[href*='download']"
            },
            "file_categories": {
                "水": ["water", "水", "液体"],
                "燃油": ["fuel", "燃油", "油料"],
                "座椅": ["seat", "座椅", "椅子"]
            }
        }
        
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                    default_config.update(loaded_config)
            except Exception as e:
                print(f"加载配置文件失败: {e}")
        
        return default_config
    
    def save_config(self):
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存配置文件失败: {e}")
    
    def setup_chrome_driver(self):
        """设置Chrome浏览器"""
        chrome_options = Options()
        
        # 设置下载路径
        download_path = os.path.abspath(self.config["download_folder"])
        if not os.path.exists(download_path):
            os.makedirs(download_path)
        
        prefs = {
            "download.default_directory": download_path,
            "download.prompt_for_download": False,
            "download.directory_upgrade": True,
            "safebrowsing.enabled": True
        }
        chrome_options.add_experimental_option("prefs", prefs)
        
        # 其他选项
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # 创建WebDriver
        if self.config["chrome_driver_path"]:
            service = Service(self.config["chrome_driver_path"])
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
        else:
            self.driver = webdriver.Chrome(options=chrome_options)
        
        # 隐藏自动化特征
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        return self.driver
    
    def login(self, username=None, password=None):
        """登录网站"""
        if not self.driver:
            self.setup_chrome_driver()
        
        username = username or self.config["username"]
        password = password or self.config["password"]
        
        if not username or not password:
            raise ValueError("用户名和密码不能为空")
        
        try:
            # 访问登录页面
            self.driver.get(self.config["website_url"])
            
            # 等待页面加载
            wait = WebDriverWait(self.driver, 10)
            
            # 输入用户名
            username_field = wait.until(
                EC.presence_of_element_located((By.CSS_SELECTOR, self.config["login_selectors"]["username_field"]))
            )
            username_field.clear()
            username_field.send_keys(username)
            
            # 输入密码
            password_field = self.driver.find_element(By.CSS_SELECTOR, self.config["login_selectors"]["password_field"])
            password_field.clear()
            password_field.send_keys(password)
            
            # 点击登录按钮
            login_button = self.driver.find_element(By.CSS_SELECTOR, self.config["login_selectors"]["login_button"])
            login_button.click()
            
            # 等待登录完成
            time.sleep(3)
            
            # 检查是否登录成功（这里需要根据实际网站调整）
            current_url = self.driver.current_url
            if "login" not in current_url.lower() and "error" not in current_url.lower():
                self.is_logged_in = True
                print("✅ 登录成功")
                return True
            else:
                print("❌ 登录失败")
                return False
                
        except Exception as e:
            print(f"登录过程中出错: {e}")
            return False
    
    def search_files(self, flight_number, category=None):
        """搜索文件"""
        if not self.is_logged_in:
            raise Exception("请先登录")
        
        try:
            # 构建搜索关键词
            search_keywords = [flight_number]
            if category and category in self.config["file_categories"]:
                search_keywords.extend(self.config["file_categories"][category])
            
            search_query = " ".join(search_keywords)
            
            # 查找搜索框
            search_box = self.driver.find_element(By.CSS_SELECTOR, self.config["search_selectors"]["search_box"])
            search_box.clear()
            search_box.send_keys(search_query)
            
            # 点击搜索按钮
            search_button = self.driver.find_element(By.CSS_SELECTOR, self.config["search_selectors"]["search_button"])
            search_button.click()
            
            # 等待搜索结果
            time.sleep(3)
            
            # 获取下载链接
            download_links = self.driver.find_elements(By.CSS_SELECTOR, self.config["search_selectors"]["result_links"])
            
            files_info = []
            for link in download_links:
                try:
                    file_info = {
                        "title": link.text or link.get_attribute("title"),
                        "url": link.get_attribute("href"),
                        "category": category or "未分类"
                    }
                    files_info.append(file_info)
                except Exception as e:
                    print(f"获取文件信息失败: {e}")
            
            return files_info
            
        except Exception as e:
            print(f"搜索文件时出错: {e}")
            return []
    
    def download_file(self, file_info, progress_callback=None):
        """下载单个文件"""
        try:
            url = file_info["url"]
            title = file_info["title"]
            category = file_info["category"]
            
            # 创建分类文件夹
            category_folder = os.path.join(self.config["download_folder"], category)
            if not os.path.exists(category_folder):
                os.makedirs(category_folder)
            
            # 使用浏览器下载
            self.driver.get(url)
            
            if progress_callback:
                progress_callback(f"正在下载: {title}")
            
            # 等待下载完成（这里可以根据实际情况调整）
            time.sleep(5)
            
            return True
            
        except Exception as e:
            print(f"下载文件失败: {e}")
            return False
    
    def download_by_flight_number(self, flight_number, categories=None, progress_callback=None):
        """根据架次号下载文件"""
        if not self.is_logged_in:
            if not self.login():
                return False
        
        categories = categories or ["水", "燃油", "座椅"]
        all_files = []
        
        # 搜索各类文件
        for category in categories:
            if progress_callback:
                progress_callback(f"正在搜索 {category} 相关文件...")
            
            files = self.search_files(flight_number, category)
            all_files.extend(files)
            time.sleep(2)  # 避免请求过快
        
        # 下载文件
        success_count = 0
        total_count = len(all_files)
        
        for i, file_info in enumerate(all_files):
            if progress_callback:
                progress_callback(f"下载进度: {i+1}/{total_count} - {file_info['title']}")
            
            if self.download_file(file_info, progress_callback):
                success_count += 1
            
            time.sleep(1)  # 下载间隔
        
        if progress_callback:
            progress_callback(f"下载完成！成功下载 {success_count}/{total_count} 个文件")
        
        return success_count, total_count
    
    def close(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()
            self.driver = None
            self.is_logged_in = False

# 使用示例
if __name__ == "__main__":
    downloader = AutoDownloader()
    
    try:
        # 登录
        if downloader.login("your_username", "your_password"):
            # 下载架次号10110的所有文件
            success, total = downloader.download_by_flight_number("10110")
            print(f"下载完成：{success}/{total}")
    finally:
        downloader.close()
