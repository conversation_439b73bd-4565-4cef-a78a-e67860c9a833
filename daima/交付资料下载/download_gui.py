#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动化下载系统GUI界面
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import os
import json
from auto_downloader import AutoDownloader

class DownloadGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("自动化文件下载系统")
        self.root.geometry("800x600")
        self.root.configure(bg='#f0f0f0')
        
        # 初始化下载器
        self.downloader = AutoDownloader()
        self.is_downloading = False
        
        self.setup_ui()
        self.load_saved_config()
    
    def setup_ui(self):
        """设置用户界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="自动化文件下载系统", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 网站配置区域
        config_frame = ttk.LabelFrame(main_frame, text="网站配置", padding="10")
        config_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        config_frame.columnconfigure(1, weight=1)
        
        # 网站URL
        ttk.Label(config_frame, text="网站URL:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.url_var = tk.StringVar()
        self.url_entry = ttk.Entry(config_frame, textvariable=self.url_var, width=50)
        self.url_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        
        # 用户名
        ttk.Label(config_frame, text="用户名:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=(5, 0))
        self.username_var = tk.StringVar()
        self.username_entry = ttk.Entry(config_frame, textvariable=self.username_var)
        self.username_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(0, 10), pady=(5, 0))
        
        # 密码
        ttk.Label(config_frame, text="密码:").grid(row=2, column=0, sticky=tk.W, padx=(0, 10), pady=(5, 0))
        self.password_var = tk.StringVar()
        self.password_entry = ttk.Entry(config_frame, textvariable=self.password_var, show="*")
        self.password_entry.grid(row=2, column=1, sticky=(tk.W, tk.E), padx=(0, 10), pady=(5, 0))
        
        # 下载路径
        ttk.Label(config_frame, text="下载路径:").grid(row=3, column=0, sticky=tk.W, padx=(0, 10), pady=(5, 0))
        self.download_path_var = tk.StringVar()
        self.download_path_entry = ttk.Entry(config_frame, textvariable=self.download_path_var)
        self.download_path_entry.grid(row=3, column=1, sticky=(tk.W, tk.E), padx=(0, 10), pady=(5, 0))
        
        ttk.Button(config_frame, text="浏览", command=self.browse_download_path).grid(row=3, column=2, padx=(5, 0), pady=(5, 0))
        
        # 保存配置按钮
        ttk.Button(config_frame, text="保存配置", command=self.save_config).grid(row=4, column=1, pady=(10, 0))
        
        # 下载区域
        download_frame = ttk.LabelFrame(main_frame, text="下载设置", padding="10")
        download_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        download_frame.columnconfigure(1, weight=1)
        
        # 架次号
        ttk.Label(download_frame, text="架次号:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.flight_number_var = tk.StringVar()
        self.flight_number_entry = ttk.Entry(download_frame, textvariable=self.flight_number_var, font=("Arial", 12))
        self.flight_number_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        
        # 文件类型选择
        ttk.Label(download_frame, text="文件类型:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=(10, 0))
        
        # 复选框框架
        checkbox_frame = ttk.Frame(download_frame)
        checkbox_frame.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(0, 10), pady=(10, 0))
        
        self.water_var = tk.BooleanVar(value=True)
        self.fuel_var = tk.BooleanVar(value=True)
        self.seat_var = tk.BooleanVar(value=True)
        
        ttk.Checkbutton(checkbox_frame, text="水相关文件", variable=self.water_var).grid(row=0, column=0, sticky=tk.W, padx=(0, 20))
        ttk.Checkbutton(checkbox_frame, text="燃油相关文件", variable=self.fuel_var).grid(row=0, column=1, sticky=tk.W, padx=(0, 20))
        ttk.Checkbutton(checkbox_frame, text="座椅相关文件", variable=self.seat_var).grid(row=0, column=2, sticky=tk.W)
        
        # 操作按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=3, pady=(10, 0))
        
        self.login_button = ttk.Button(button_frame, text="登录网站", command=self.login_website)
        self.login_button.grid(row=0, column=0, padx=(0, 10))
        
        self.download_button = ttk.Button(button_frame, text="开始下载", command=self.start_download, state="disabled")
        self.download_button.grid(row=0, column=1, padx=(0, 10))
        
        self.stop_button = ttk.Button(button_frame, text="停止下载", command=self.stop_download, state="disabled")
        self.stop_button.grid(row=0, column=2)
        
        # 进度显示区域
        progress_frame = ttk.LabelFrame(main_frame, text="下载进度", padding="10")
        progress_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        progress_frame.columnconfigure(0, weight=1)
        progress_frame.rowconfigure(1, weight=1)
        
        # 进度条
        self.progress_var = tk.StringVar(value="准备就绪")
        self.progress_label = ttk.Label(progress_frame, textvariable=self.progress_var)
        self.progress_label.grid(row=0, column=0, sticky=(tk.W, tk.E))
        
        # 日志文本框
        log_frame = ttk.Frame(progress_frame)
        log_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        self.log_text = tk.Text(log_frame, height=15, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(log_frame, orient="vertical", command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 配置主框架的行权重
        main_frame.rowconfigure(4, weight=1)
    
    def browse_download_path(self):
        """浏览下载路径"""
        folder = filedialog.askdirectory()
        if folder:
            self.download_path_var.set(folder)
    
    def load_saved_config(self):
        """加载保存的配置"""
        config = self.downloader.config
        self.url_var.set(config.get("website_url", ""))
        self.username_var.set(config.get("username", ""))
        self.password_var.set(config.get("password", ""))
        self.download_path_var.set(config.get("download_folder", "./downloads"))
    
    def save_config(self):
        """保存配置"""
        self.downloader.config.update({
            "website_url": self.url_var.get(),
            "username": self.username_var.get(),
            "password": self.password_var.get(),
            "download_folder": self.download_path_var.get()
        })
        self.downloader.save_config()
        self.log_message("✅ 配置已保存")
        messagebox.showinfo("成功", "配置已保存！")
    
    def log_message(self, message):
        """添加日志消息"""
        self.log_text.insert(tk.END, f"[{tk.datetime.datetime.now().strftime('%H:%M:%S')}] {message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def login_website(self):
        """登录网站"""
        def login_thread():
            try:
                self.login_button.config(state="disabled")
                self.log_message("🔄 正在登录网站...")
                self.progress_var.set("正在登录...")
                
                username = self.username_var.get()
                password = self.password_var.get()
                
                if not username or not password:
                    messagebox.showerror("错误", "请输入用户名和密码！")
                    return
                
                if self.downloader.login(username, password):
                    self.log_message("✅ 登录成功！")
                    self.progress_var.set("登录成功，可以开始下载")
                    self.download_button.config(state="normal")
                else:
                    self.log_message("❌ 登录失败！")
                    self.progress_var.set("登录失败")
                    messagebox.showerror("错误", "登录失败，请检查用户名和密码！")
                    
            except Exception as e:
                self.log_message(f"❌ 登录出错: {e}")
                messagebox.showerror("错误", f"登录出错: {e}")
            finally:
                self.login_button.config(state="normal")
        
        threading.Thread(target=login_thread, daemon=True).start()
    
    def start_download(self):
        """开始下载"""
        flight_number = self.flight_number_var.get().strip()
        if not flight_number:
            messagebox.showerror("错误", "请输入架次号！")
            return
        
        # 获取选择的文件类型
        categories = []
        if self.water_var.get():
            categories.append("水")
        if self.fuel_var.get():
            categories.append("燃油")
        if self.seat_var.get():
            categories.append("座椅")
        
        if not categories:
            messagebox.showerror("错误", "请至少选择一种文件类型！")
            return
        
        def download_thread():
            try:
                self.is_downloading = True
                self.download_button.config(state="disabled")
                self.stop_button.config(state="normal")
                
                self.log_message(f"🚀 开始下载架次号 {flight_number} 的文件...")
                self.log_message(f"📂 文件类型: {', '.join(categories)}")
                
                def progress_callback(message):
                    self.progress_var.set(message)
                    self.log_message(message)
                
                success, total = self.downloader.download_by_flight_number(
                    flight_number, categories, progress_callback
                )
                
                self.log_message(f"🎉 下载完成！成功下载 {success}/{total} 个文件")
                self.progress_var.set(f"下载完成：{success}/{total}")
                
                if success > 0:
                    messagebox.showinfo("完成", f"下载完成！\n成功下载 {success}/{total} 个文件")
                else:
                    messagebox.showwarning("警告", "没有找到相关文件或下载失败！")
                    
            except Exception as e:
                self.log_message(f"❌ 下载出错: {e}")
                messagebox.showerror("错误", f"下载出错: {e}")
            finally:
                self.is_downloading = False
                self.download_button.config(state="normal")
                self.stop_button.config(state="disabled")
        
        threading.Thread(target=download_thread, daemon=True).start()
    
    def stop_download(self):
        """停止下载"""
        self.is_downloading = False
        self.log_message("⏹️ 用户停止下载")
        self.progress_var.set("下载已停止")
        self.download_button.config(state="normal")
        self.stop_button.config(state="disabled")
    
    def on_closing(self):
        """关闭程序时的清理工作"""
        if self.downloader:
            self.downloader.close()
        self.root.destroy()

def main():
    root = tk.Tk()
    app = DownloadGUI(root)
    root.protocol("WM_DELETE_WINDOW", app.on_closing)
    root.mainloop()

if __name__ == "__main__":
    main()
