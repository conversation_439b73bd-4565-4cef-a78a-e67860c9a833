# 自动化文件下载系统

## 📋 功能介绍

这是一个基于Python的自动化文件下载系统，可以根据架次号自动登录网站并下载相关文件。

### 主要功能
- 🔐 自动登录网站
- 🔍 根据架次号搜索文件
- 📂 按类型分类下载（水、燃油、座椅相关文件）
- 🖥️ 友好的图形界面
- ⚙️ 可配置的搜索和下载参数

## 🛠️ 安装要求

### 1. Python环境
- Python 3.7 或更高版本

### 2. 依赖包
```bash
pip install selenium requests pillow
```

### 3. Chrome浏览器和ChromeDriver
- 安装最新版Chrome浏览器
- 下载对应版本的ChromeDriver：https://chromedriver.chromium.org/
- 将ChromeDriver添加到系统PATH中

## 🚀 使用方法

### 1. 启动程序
```bash
python main.py
```

### 2. 配置网站信息
- 输入目标网站URL
- 输入登录用户名和密码
- 设置下载保存路径
- 点击"保存配置"

### 3. 登录网站
- 点击"登录网站"按钮
- 等待登录成功提示

### 4. 下载文件
- 输入架次号（如：10110）
- 选择要下载的文件类型
- 点击"开始下载"

## ⚙️ 配置说明

### config.json 配置文件
```json
{
  "website_url": "网站登录页面URL",
  "username": "用户名",
  "password": "密码",
  "download_folder": "下载保存路径",
  "login_selectors": {
    "username_field": "用户名输入框的CSS选择器",
    "password_field": "密码输入框的CSS选择器",
    "login_button": "登录按钮的CSS选择器"
  },
  "search_selectors": {
    "search_box": "搜索框的CSS选择器",
    "search_button": "搜索按钮的CSS选择器",
    "result_links": "下载链接的CSS选择器"
  },
  "file_categories": {
    "水": ["water", "水", "液体"],
    "燃油": ["fuel", "燃油", "油料"],
    "座椅": ["seat", "座椅", "椅子"]
  }
}
```

## 🔧 自定义配置

### 1. 修改CSS选择器
根据目标网站的实际HTML结构，修改`config.json`中的CSS选择器：

```json
"login_selectors": {
  "username_field": "#username",
  "password_field": "#password", 
  "login_button": ".login-btn"
}
```

### 2. 添加文件类型
在`file_categories`中添加新的文件类型和关键词：

```json
"file_categories": {
  "水": ["water", "水", "液体", "饮用水"],
  "燃油": ["fuel", "燃油", "油料", "航油"],
  "座椅": ["seat", "座椅", "椅子", "客舱"],
  "新类型": ["keyword1", "keyword2"]
}
```

## 📁 文件结构

```
交付资料下载/
├── main.py              # 主启动程序
├── auto_downloader.py   # 核心下载逻辑
├── download_gui.py      # GUI界面
├── config.json          # 配置文件
├── common_utils.py      # 通用工具函数
├── downloads/           # 默认下载目录
└── README.md           # 说明文档
```

## 🐛 常见问题

### 1. ChromeDriver问题
**错误**：`selenium.common.exceptions.WebDriverException`

**解决**：
- 确保Chrome浏览器是最新版本
- 下载对应版本的ChromeDriver
- 将ChromeDriver路径添加到系统PATH

### 2. 登录失败
**可能原因**：
- 用户名密码错误
- 网站结构变化，CSS选择器需要更新
- 网站有验证码或其他安全措施

**解决**：
- 检查用户名密码
- 使用浏览器开发者工具查看实际的HTML结构
- 更新`config.json`中的选择器

### 3. 搜索不到文件
**可能原因**：
- 架次号不存在
- 搜索关键词不匹配
- 搜索结果页面结构变化

**解决**：
- 确认架次号正确
- 调整`file_categories`中的关键词
- 更新搜索相关的CSS选择器

## 📞 技术支持

如果遇到问题，请检查：
1. Python版本和依赖包
2. Chrome和ChromeDriver版本匹配
3. 网站结构是否发生变化
4. 配置文件是否正确

## 🔒 安全说明

- 密码会保存在本地配置文件中，请确保文件安全
- 建议使用专门的下载账户
- 遵守网站的使用条款和robots.txt规则
