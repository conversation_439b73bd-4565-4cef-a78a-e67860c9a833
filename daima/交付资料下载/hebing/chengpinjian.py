from tkinter import *

import os
import requests
import pandas as pd
from selenium import webdriver
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.by import By


def pack_ATR():
   
  
  # Read SN info from `1.xls`
  df = pd.read_excel('D:/空调组件清单/空调组件系列号.xlsx')
  
  # Initialize WebDriver with options
  options = webdriver.ChromeOptions()
  options.add_experimental_option('excludeSwitches', ['enable-logging'])
  options.add_experimental_option("prefs", {"download_restrictions": 0})
  options.add_argument("--ignore-certificate-errors")
  options.add_argument("--ignore-ssl-errors")
  options.add_argument("--unsafely-treat-insecure-origin-as-secure=http://mom.comac.int/")
  options.add_argument("enable-automation")

  # Function to handle each SN
  def process_sn(sn):
      # Create new folder path
      folder_name = sn
      save_dir = os.path.join('D:\空调组件清单', folder_name)
      if not os.path.exists(save_dir):
        os.makedirs(save_dir)
    
      prefs = {"download.default_directory": save_dir}
      options.add_experimental_option("prefs", prefs)
    
      driver = webdriver.Chrome(options=options)
      driver.get('http://mom.comac.int/')
    
      # Login sequence
      username = WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.CLASS_NAME, 'username')))
      username.send_keys('338146')
    
      password = WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.CLASS_NAME, 'pwd')))
      password.send_keys('CoioM@6t62!')
    
      login_button = WebDriverWait(driver, 60).until(EC.element_to_be_clickable((By.CLASS_NAME, 'btn')))
      login_button.click()
      print("Login button clicked")
    
      # Wait for the next page to load
      WebDriverWait(driver, 60).until(lambda driver: driver.execute_script("return document.readyState") == "complete")
    
      # Navigate through menus
      menu_item = WebDriverWait(driver, 60).until(
       EC.element_to_be_clickable((By.XPATH, '//div[contains(@class, "navigator-primary-menu") and normalize-space(text())="质量管理"]'))
      )
      menu_item.click()
      print("导航栏 '质量管理' 项被点击")
    
      sub_menu_item = WebDriverWait(driver, 60).until(EC.element_to_be_clickable((By.XPATH, '//li[text()="QAR任务查询"]')))
      sub_menu_item.click()
      print("QAR任务查询被点击")
    
      sub_menu_item1 = WebDriverWait(driver, 60).until(
          EC.element_to_be_clickable((By.XPATH, '//div[contains(@class, "ark-tabs") and normalize-space(text())="全部QAR"]'))
      )
      sub_menu_item1.click()
      print("已选全部QAR")
    
      # Input part number and SN
      part_number_input = WebDriverWait(driver, 60).until(
          EC.presence_of_element_located((By.XPATH, '//div[@class="formlayout-Item" and contains(.,"材料/零件图号")]//input[@class="ark-input ark-input-default"]'))
      )
      part_number_input.send_keys("3525A020001")
      print("材料/零件图号输入完成")
    
      sn_input = WebDriverWait(driver, 60).until(
          EC.presence_of_element_located((By.XPATH, '(//div[@class="formlayout-Item" and contains(.,"序列号")]//input[@class="ark-input ark-input-default"])[2]'))
      )
      sn_input.send_keys(sn)
      print("序列号输入完成")
    
      search_button = WebDriverWait(driver, 60).until(EC.element_to_be_clickable((By.XPATH, '//button[text()="查找"]')))
      search_button.click()
      print("已点击查找")
    
      # Click the first QAR link
      qar_elements = WebDriverWait(driver, 60).until(EC.presence_of_all_elements_located((By.XPATH, '//a[contains(text(), "QAR")]')))
      for qar_element in qar_elements:
          if qar_element.text.startswith("QAR"):
              qar_element.click()
              print("已点击该份QAR")
              break
    
      # Wait for page to load and click download buttons
      WebDriverWait(driver, 60).until(lambda driver: driver.execute_script("return document.readyState") == "complete")
      download_buttons = WebDriverWait(driver, 60).until(
          EC.presence_of_all_elements_located((By.XPATH, '//i[@class="iconfont iconmd-cloud-download"]'))
      )
      for button in download_buttons:
          button.click()
          print("Download button clicked")
    
      driver.get_screenshot_as_file(os.path.join(save_dir, 'preview.png'))


  # Process each SN
  for index, row in df.iterrows():
      sn = row['SN']  # Assuming 'SN' is the column name in `1.xls`
      process_sn(sn)

  print("All tasks completed.")
  input("请点击任何按钮退出")

root=Tk()
root.wm_title('空调组件ATR下载器')


w=Label(root,text='请将需要下载的空调组件序列号填写在"D:\空调组件清单空调组件系列号.xlsx"的第一列中，然后单击下载')
w.grid(row=1,column=0,sticky=W)

b=Button(root,text='下载',command=pack_ATR)
b.grid(row=2,column=0,sticky=E)

root.mainloop()


