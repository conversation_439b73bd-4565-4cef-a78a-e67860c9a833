#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试图片插入逻辑
"""

import sqlite3
import os

def test_image_insertion_logic():
    """测试图片插入的逻辑"""
    
    print("🖼️ 图片插入逻辑测试")
    print("=" * 60)
    
    db_path = r"D:\shangfei\sf\11\daima\CR\shujuku.db"
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 查询有图片的文档
        cursor.execute("""
            SELECT DISTINCT w.CR_number, w.document_path, COUNT(i.image_data) as image_count
            FROM WordData w
            LEFT JOIN Images i ON w.document_path = i.document_path
            WHERE i.image_data IS NOT NULL
            GROUP BY w.CR_number, w.document_path
            ORDER BY image_count DESC
            LIMIT 5
        """)
        
        rows = cursor.fetchall()
        
        if not rows:
            print("❌ 数据库中没有找到包含图片的文档")
            conn.close()
            return
        
        print(f"📊 找到 {len(rows)} 个包含图片的文档:")
        print("-" * 60)
        
        for cr_number, doc_path, image_count in rows:
            print(f"\n📋 CR编号: {cr_number}")
            print(f"   📁 文档路径: {doc_path}")
            print(f"   📄 原文档名: {os.path.basename(doc_path)}")
            print(f"   🖼️ 图片数量: {image_count}")
            
            print(f"   📝 将生成的附图标题:")
            for i in range(image_count):
                print(f"      - 18附图（{i+1}）")
                print(f"        Sketch（{i+1}）")
                print(f"        2编号：{cr_number}")
                print(f"        原文档：{os.path.basename(doc_path)}")
                if i < image_count - 1:
                    print(f"        [分页符]")
            print("-" * 40)
        
        conn.close()
        
        print("\n✅ 图片插入规则:")
        print("   1. 第3页开始插入图片")
        print("   2. 每张图片包含:")
        print("      - 18附图（序号）")
        print("      - Sketch（序号）")
        print("      - 2编号：CR编号")
        print("      - 原文档：原始文档名")
        print("   3. 多张图片之间用分页符分隔")
        
    except Exception as e:
        print(f"❌ 数据库查询失败: {e}")

def simulate_image_titles():
    """模拟生成图片标题"""
    
    print("\n" + "=" * 60)
    print("📝 图片标题生成示例")
    print("=" * 60)
    
    # 模拟数据
    test_cases = [
        {"cr_number": "CR-ARJ10177-001", "doc_name": "CR-ARJ10177-001.docx", "image_count": 1},
        {"cr_number": "CR-ARJ10177-002", "doc_name": "CR-ARJ10177-002.docx", "image_count": 3},
        {"cr_number": "CR-ARJ10177-003", "doc_name": "CR-ARJ10177-003.docx", "image_count": 2},
    ]
    
    for case in test_cases:
        print(f"\n📋 测试案例: {case['cr_number']}")
        print(f"   原文档: {case['doc_name']}")
        print(f"   图片数量: {case['image_count']}")
        print("   生成的页面内容:")
        
        for i in range(case['image_count']):
            page_num = 3 + i
            print(f"\n   📄 第{page_num}页:")
            print(f"      18附图（{i+1}）")
            print(f"      Sketch（{i+1}）")
            print(f"      2编号：{case['cr_number']}")
            print(f"      原文档：{case['doc_name']}")
            print(f"      [图片内容]")
            
            if i < case['image_count'] - 1:
                print(f"      [分页符 → 第{page_num+1}页]")
        
        print("-" * 40)

if __name__ == "__main__":
    test_image_insertion_logic()
    simulate_image_titles()
    
    print("\n" + "=" * 60)
    print("🎯 总结:")
    print("✅ 第1页：区域 + 位置 + 初始状态")
    print("✅ 第2页：最终状态")
    print("✅ 第3页开始：附图（带完整标题和信息）")
    print("✅ 每张图片独立页面，包含编号和CR信息")
    
    input("\n按回车键退出...")
