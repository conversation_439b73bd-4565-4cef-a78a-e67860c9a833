import os
import sqlite3
from docx import Document
from docx.shared import Inches
import io
from PIL import Image
from docx.enum.text import WD_BREAK
import time
import pytest

def parse_divergence_descriptions(divergence_text):
    """解析偏差描述，分离初始状态和最终状态"""
    if not divergence_text:
        return "", ""
    
    final_keywords = ["最终状态：", "Final state"]
    
    for keyword in final_keywords:
        pos = divergence_text.find(keyword)
        if pos != -1:
            return divergence_text[:pos].strip(), divergence_text[pos:].strip()
    
    return divergence_text.strip(), ""

class TestDocumentRestore:
    """文档还原测试类"""
    
    def setup_method(self):
        """测试前的设置"""
        self.db_path = r"D:\shangfei\sf\11\daima\CR\shujuku.db"
        self.template_path = r"D:\shangfei\sf\11\daima\CR\CR muban.docx"
        self.output_folder = r"D:\shangfei\sf\11\daima\CR\10177\客服已完成-还原"
        
        # 确保输出目录存在
        if not os.path.exists(self.output_folder):
            os.makedirs(self.output_folder)
    
    def test_files_exist(self):
        """测试必要文件是否存在"""
        print(f"\n🔍 检查文件存在性...")
        
        assert os.path.exists(self.template_path), f"模板文件不存在: {self.template_path}"
        assert os.path.exists(self.db_path), f"数据库文件不存在: {self.db_path}"
        
        print(f"✅ 模板文件存在: {self.template_path}")
        print(f"✅ 数据库文件存在: {self.db_path}")
    
    def test_database_connection(self):
        """测试数据库连接和数据"""
        print(f"\n🔗 测试数据库连接...")
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 检查WordData表
        cursor.execute("SELECT COUNT(*) FROM WordData")
        word_count = cursor.fetchone()[0]
        assert word_count > 0, "WordData表中没有数据"
        
        # 检查Images表
        cursor.execute("SELECT COUNT(*) FROM Images")
        image_count = cursor.fetchone()[0]
        
        print(f"✅ WordData表中有 {word_count} 条记录")
        print(f"✅ Images表中有 {image_count} 张图片")
        
        conn.close()
    
    def test_divergence_parsing(self):
        """测试偏差描述解析功能"""
        print(f"\n📝 测试偏差描述解析...")
        
        test_text = """区域： 
area
位置：
Localization
初始状态：
Initial state content
最终状态：
Final state content"""
        
        first_page, second_page = parse_divergence_descriptions(test_text)
        
        assert "区域：" in first_page, "第一页应包含区域信息"
        assert "位置：" in first_page, "第一页应包含位置信息"
        assert "初始状态：" in first_page, "第一页应包含初始状态"
        assert "最终状态：" in second_page, "第二页应包含最终状态"
        assert "区域：" not in second_page, "第二页不应包含区域信息"
        
        print(f"✅ 偏差描述解析正确")
        print(f"   第一页长度: {len(first_page)} 字符")
        print(f"   第二页长度: {len(second_page)} 字符")
    
    def test_single_document_restore(self):
        """测试单文档还原功能"""
        print(f"\n🧪 测试单文档还原...")
        
        start_time = time.time()
        
        # 连接数据库
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 获取第一份文档
        cursor.execute("SELECT * FROM WordData LIMIT 1")
        row = cursor.fetchone()
        assert row is not None, "数据库中没有文档数据"
        
        # 获取列名
        cursor.execute("PRAGMA table_info(WordData)")
        columns = [column[1] for column in cursor.fetchall()]
        
        # 转换为字典
        data = dict(zip(columns, row))
        
        print(f"📋 测试文档: {data.get('CR_number', 'N/A')}")
        print(f"📁 原文档: {os.path.basename(data.get('document_path', ''))}")
        
        # 查询图片
        cursor.execute("SELECT image_data FROM Images WHERE document_path = ?", (data["document_path"],))
        image_rows = cursor.fetchall()
        
        print(f"🖼️ 图片数量: {len(image_rows)}")
        
        conn.close()
        
        # 加载模板
        doc = Document(self.template_path)
        original_table_count = len(doc.tables)
        
        # 解析偏差描述
        divergence_text = data.get("divergence_descriptions", "")
        first_page_content, second_page_content = parse_divergence_descriptions(divergence_text)
        
        # 填充表格数据
        tables_processed = 0
        for table_index, table in enumerate(doc.tables[:2]):
            tables_processed += 1
            
            for row_index, row in enumerate(table.rows):
                for col_index, cell in enumerate(row.cells):
                    text = cell.text.strip()
                    
                    # 测试基本字段填充
                    if "2 编号" in text and col_index + 1 < len(row.cells):
                        table.cell(row_index, col_index + 1).text = data.get("CR_number", "") or ""
                    elif "14 偏差描述：" in text or "Description of divergence" in text:
                        next_row_index = row_index + 1
                        if next_row_index < len(table.rows):
                            content = first_page_content if table_index == 0 else second_page_content
                            table.rows[next_row_index].cells[col_index].text = content
        
        print(f"✅ 处理了 {tables_processed} 个表格")
        
        # 处理图片
        images_processed = 0
        if image_rows:
            doc.add_paragraph().add_run().add_break(WD_BREAK.PAGE)
            
            cr_number = data.get("CR_number", "")
            original_doc_name = os.path.basename(data["document_path"])
            
            for i, image_row in enumerate(image_rows):
                try:
                    image_data = image_row[0]
                    image_stream = io.BytesIO(image_data)
                    img = Image.open(image_stream)
                    
                    # 添加标题
                    title_paragraph = doc.add_paragraph()
                    title_run = title_paragraph.add_run(f"18附图（{i+1}）")
                    title_run.bold = True
                    title_paragraph.add_run(f"\nSketch（{i+1}）")
                    
                    # 添加信息
                    info_paragraph = doc.add_paragraph()
                    info_paragraph.add_run(f"2编号：{cr_number}")
                    info_paragraph.add_run(f"\n原文档：{original_doc_name}")
                    
                    # 保存临时图片
                    temp_image_path = os.path.join(self.output_folder, f"temp_pytest_image_{i}.png")
                    img.save(temp_image_path)
                    
                    # 插入图片
                    doc.add_picture(temp_image_path, width=Inches(6))
                    
                    # 删除临时文件
                    os.remove(temp_image_path)
                    
                    # 分页
                    if i < len(image_rows) - 1:
                        doc.add_paragraph().add_run().add_break(WD_BREAK.PAGE)
                    
                    images_processed += 1
                    
                except Exception as e:
                    print(f"⚠️ 图片 {i+1} 处理失败: {e}")
        
        print(f"✅ 处理了 {images_processed} 张图片")
        
        # 保存文档
        output_file_name = os.path.basename(data["document_path"])
        file_name, file_ext = os.path.splitext(output_file_name)
        new_file_name = file_name + "_pytest测试还原" + file_ext
        output_file_path = os.path.join(self.output_folder, new_file_name)
        doc.save(output_file_path)
        
        end_time = time.time()
        process_time = end_time - start_time
        
        print(f"✅ 文档还原完成")
        print(f"⏱️ 处理时间: {process_time:.2f}秒")
        print(f"📁 输出文件: {new_file_name}")
        
        # 验证输出文件存在
        assert os.path.exists(output_file_path), f"输出文件不存在: {output_file_path}"
        
        # 验证文件大小
        file_size = os.path.getsize(output_file_path)
        assert file_size > 1000, f"输出文件太小，可能有问题: {file_size} bytes"
        
        print(f"✅ 输出文件验证通过 ({file_size} bytes)")

if __name__ == "__main__":
    # 如果直接运行，执行所有测试
    print("🧪 运行文档还原测试套件")
    print("=" * 50)
    
    test_instance = TestDocumentRestore()
    test_instance.setup_method()
    
    try:
        test_instance.test_files_exist()
        test_instance.test_database_connection()
        test_instance.test_divergence_parsing()
        test_instance.test_single_document_restore()
        
        print(f"\n🎉 所有测试通过！")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    input("\n按回车键退出...")
