#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文档还原运行脚本
提供简单的界面来运行文档还原功能
"""

import os
import sys
from document_restore_enhanced import DocumentRestorer

def main():
    print("=" * 50)
    print("Word文档数据库还原工具")
    print("=" * 50)
    
    # 默认配置
    db_path = r"D:\shangfei\sf\11\daima\CR\shujuku.db"
    template_path = r"D:\shangfei\sf\11\daima\CR\10177\客服已完成\CR-ARJ10177-001.docx"
    output_folder = r"D:\shangfei\sf\11\daima\CR\10177\客服已完成-还原"
    
    # 检查文件是否存在
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        input("按回车键退出...")
        return
    
    if not os.path.exists(template_path):
        print(f"❌ 模板文件不存在: {template_path}")
        input("按回车键退出...")
        return
    
    print(f"📁 数据库路径: {db_path}")
    print(f"📄 模板文件: {template_path}")
    print(f"💾 输出文件夹: {output_folder}")
    print()
    
    # 创建还原器
    restorer = DocumentRestorer(db_path, template_path, output_folder)
    
    while True:
        print("请选择操作:")
        print("1. 还原所有文档")
        print("2. 还原指定CR编号的文档")
        print("3. 查看数据库中的文档列表")
        print("4. 测试偏差描述解析")
        print("5. 退出")
        
        choice = input("请输入选项 (1-5): ").strip()
        
        if choice == "1":
            print("\n开始还原所有文档...")
            restorer.restore_all_documents()
            print("\n还原完成！")
            
        elif choice == "2":
            cr_number = input("请输入CR编号: ").strip()
            if cr_number:
                print(f"\n开始还原CR编号为 {cr_number} 的文档...")
                restorer.restore_single_document(cr_number)
            else:
                print("❌ CR编号不能为空")
                
        elif choice == "3":
            print("\n查询数据库中的文档...")
            show_document_list(restorer)
            
        elif choice == "4":
            print("\n测试偏差描述解析...")
            test_divergence_parsing()

        elif choice == "5":
            print("👋 再见！")
            break
            
        else:
            print("❌ 无效选项，请重新选择")
        
        print("\n" + "-" * 50 + "\n")

def show_document_list(restorer):
    """显示数据库中的文档列表"""
    try:
        restorer.connect_database()
        
        # 查询文档基本信息
        restorer.cursor.execute("""
            SELECT CR_number, CR_version, FRR_number, aircraft_type, 
                   part_description, document_path 
            FROM WordData 
            ORDER BY CR_number
        """)
        
        rows = restorer.cursor.fetchall()
        restorer.close_database()
        
        if not rows:
            print("❌ 数据库中没有文档数据")
            return
        
        print(f"📋 数据库中共有 {len(rows)} 个文档:")
        print("-" * 100)
        print(f"{'序号':<4} {'CR编号':<15} {'版次':<8} {'FRR编号':<15} {'机型':<10} {'零件名称':<20}")
        print("-" * 100)
        
        for i, row in enumerate(rows, 1):
            cr_number = row[0] or "N/A"
            cr_version = row[1] or "N/A"
            frr_number = row[2] or "N/A"
            aircraft_type = row[3] or "N/A"
            part_description = row[4] or "N/A"
            
            # 截断过长的文本
            if len(part_description) > 18:
                part_description = part_description[:15] + "..."
            
            print(f"{i:<4} {cr_number:<15} {cr_version:<8} {frr_number:<15} {aircraft_type:<10} {part_description:<20}")
        
        print("-" * 100)
        
    except Exception as e:
        print(f"❌ 查询失败: {e}")

def test_divergence_parsing():
    """测试偏差描述解析功能"""
    from test_divergence_parsing import test_parsing, test_with_database

    print("运行偏差描述解析测试...")
    test_parsing()
    test_with_database()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，程序退出")
    except Exception as e:
        print(f"\n❌ 程序出错: {e}")
        input("按回车键退出...")
