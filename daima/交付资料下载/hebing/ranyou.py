from tkinter import *

import os
import requests
import pandas as pd
from nltk import download
from panel.pane import Alert
from selenium import webdriver
from selenium.webdriver import ActionChains
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.by import By
from sympy.physics.units import action


# C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\selenium-3.8.0.tar.gz

def pack_ATR():
    # Read SN info from `1.xls`
    df = pd.read_excel('D:/燃油清单/燃油制造序列号.xlsx', dtype=str)

    # Initialize WebDriver with options
    options = webdriver.ChromeOptions()
    options.add_experimental_option('excludeSwitches', ['enable-logging'])
    options.add_experimental_option("prefs", {"download_restrictions": 0})
    options.add_argument("--ignore-certificate-errors")
    options.add_argument("--ignore-ssl-errors")
    options.add_argument("--unsafely-treat-insecure-origin-as-secure=http://mom.comac.int/")
    options.add_argument("enable-automation")

    # Function to handle each SN
    def process_sn(sn):
        # Create new folder path
        folder_name = sn
        save_dir = os.path.join('D:\燃油清单', folder_name)
        if not os.path.exists(save_dir):
            os.makedirs(save_dir)

        prefs = {"download.default_directory": save_dir}
        options.add_experimental_option("prefs", prefs)

        driver = webdriver.Chrome(options=options)
        driver.get('http://mom.comac.int/')

        # Login sequence
        username = WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.CLASS_NAME, 'username')))
        username.send_keys('338146')

        password = WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.CLASS_NAME, 'pwd')))
        password.send_keys('CoioM@6t62!')

        login_button = WebDriverWait(driver, 60).until(EC.element_to_be_clickable((By.CLASS_NAME, 'btn')))
        login_button.click()
        print("Login button clicked")

        # Wait for the next page to load
        WebDriverWait(driver, 60).until(
            lambda driver: driver.execute_script("return document.readyState") == "complete")

        # Navigate through menus
        menu_item = WebDriverWait(driver, 60).until(
            EC.element_to_be_clickable(
                (By.XPATH, '//div[contains(@class, "navigator-primary-menu") and normalize-space(text())="生产执行"]'))
        )
        menu_item.click()
        # print("导航栏 '质量管理' 项被点击")
        print("导航栏 '生产执行' 项被点击")

        sub_menu_item = WebDriverWait(driver, 60).until(
            EC.element_to_be_clickable((By.XPATH, '//li[text()="AOSR"]')))
        # EC.element_to_be_clickable((By.XPATH, '//li[text()="QAR任务查询"]')))
        sub_menu_item.click()
        print("AOSR被点击")

        sub_menu_item1 = WebDriverWait(driver, 60).until(
            EC.element_to_be_clickable((By.XPATH, '//span[normalize-space(text())="进入AOSR查询"]'))
        )
        sub_menu_item1.click()
        print("已选进入AOSR查询")

        droodown_icon = WebDriverWait(driver, 60).until(
            EC.element_to_be_clickable(
                (By.XPATH, '//div[@id="dropDownSelectFilter"]//div[@class="ark-fkrp-select-icon"]'))
        )
        droodown_icon.click()
        print("下拉框被点击")

        droodown_icon = WebDriverWait(driver, 60).until(
            EC.element_to_be_clickable(
                (By.XPATH, '//div[@class="ark-fktable"]//tr[@data-index="5"]'))
        )
        droodown_icon.click()
        print("被点击")

        # part_number_input2 = WebDriverWait(driver, 60).until(
        #     EC.presence_of_element_located((By.XPATH,
        #                                     '//div[@class="formlayout-Item" and contains(.,"AO编号")]//input[@class="ark-input ark-input-default"]')))
        # part_number_input2.send_keys("140A105ZF2890")
        # print("AO编号输入完成")

        part_number_input2 = WebDriverWait(driver, 60).until(
            EC.presence_of_element_located((By.XPATH,
                                            '//div[@class="formlayout-Item" and contains(.,"AO名称")]//input[@class="ark-input ark-input-default"]')))
        part_number_input2.send_keys("*燃油箱微生物*")
        print("*燃油箱微生物*已输入完成")

        sn_input = WebDriverWait(driver, 60).until(
            EC.presence_of_element_located((By.XPATH,
                                            '(//div[@class="formlayout-Item" and contains(.,"制造序列号")]//input[@class="ark-input ark-input-default"])')))
        sn_input.send_keys(sn)
        print("制造序列号输入完成")

        search_button = WebDriverWait(driver, 60).until(
            EC.element_to_be_clickable((By.XPATH, '//button[text()="查找"]')))
        search_button.click()
        print("已点击查找")

        droodown_icon = WebDriverWait(driver, 60).until(
            EC.visibility_of_element_located((By.XPATH, '//span[normalize-space(text())="燃油箱微生物污染检测及处理"]'))
        )
        action = ActionChains(driver)
        action.double_click(droodown_icon).perform()
        print("燃油箱微生物污染检测及处理")

        droodown_icon = WebDriverWait(driver, 60).until(
            EC.element_to_be_clickable((By.XPATH, '//button[text()="附件管理"]'))
        )
        droodown_icon.click()
        print("附件管理被点击")

        droodown_icon = WebDriverWait(driver, 60).until(
            EC.element_to_be_clickable(
                (By.XPATH, '//div[@class="ark-table-cell ark-table-cell-ellipsis"and contains(.,"质量文件")]'))
        )
        droodown_icon.click()
        print("质量文件被点击")

        download_buttons = WebDriverWait(driver, 60).until(
            EC.element_to_be_clickable((By.XPATH, '//span[contains(text(),"微生物报告.pdf")]'))
        )
        download_buttons.click()
        print("微生物报告.pdf已下载")
        driver.get_screenshot_as_file(os.path.join(save_dir, 'preview.png'))

    # Process each SN
    for index, row in df.iterrows():
        sn = row['SN']  # Assuming 'SN' is the column name in `1.xls`
        process_sn(sn)

    print("All tasks completed.")
    input("请点击任何按钮退出")


root = Tk()
root.wm_title('空调组件ATR下载器')

w = Label(root, text='请将需要下载的空调组件序列号填写在"D:\空调组件清单空调组件系列号.xlsx"的第一列中，然后单击下载')
w.grid(row=1, column=0, sticky=W)

b = Button(root, text='下载', command=pack_ATR)
b.grid(row=2, column=0, sticky=E)

root.mainloop()


